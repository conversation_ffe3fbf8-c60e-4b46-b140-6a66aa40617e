use std::ops::Mul;

use candle_core::Tensor;
use candle_nn::{Conv1dConfig, Init, Mo<PERSON>le, VarBuilder, VarMap, ops::softmax};

#[derive(Clone)]
pub struct MatmulLayer {
    pub dataset_var: DatasetVar,
}

#[derive(Clone, Debug)]
pub struct DatasetVar {
    pub weight: Tensor,
}

impl DatasetVar {
    fn dataset_var(dataset: Tensor, vb: &VarBuilder, name: &str) -> anyhow::Result<DatasetVar> {
        let weight = vb.get_with_hints(
            dataset.dims(),
            format!("dataset_weight_{name}").as_str(),
            candle_nn::Init::Randn {
                mean: 0.5,
                stdev: 0.25,
            }, // This will be overwritten
        )?;

        Ok(DatasetVar { weight })
    }
}

impl MatmulLayer {
    pub fn load(vb: &VarBuilder, dataset: Tensor, name: &str) -> anyhow::Result<Self> {
        let dataset_var = DatasetVar::dataset_var(dataset, &vb,name)?;

        Ok(Self {
            dataset_var,
        })
    }

    pub fn init(&self, dataset: Tensor, mut vm: VarMap) -> anyhow::Result<VarMap> {
        vm.set_one("dataset_weight".to_string(), dataset)?;
        Ok(vm)
    }

    /// returns: pred_t, pred_t_conv, norm_probs
    pub fn forward(&self, input_tensor: &Tensor) -> candle_core::Result<(Tensor, Tensor,Tensor)> {
        let input_reshaped = input_tensor.reshape(&[input_tensor.dims().iter().cloned().reduce(|a, b| a.mul(&b)).unwrap(), 1usize])?;
        let probs = self.dataset_var.weight.matmul(&input_reshaped)?;
        let norm_probs = softmax(&probs, 0)?;
        let pred_t = norm_probs.t()?.matmul(&self.dataset_var.weight)?;
        let pred_probs = softmax(&self.dataset_var.weight.matmul(&pred_t)?,0)?;

        Ok((pred_t, norm_probs, pred_probs))
    }
}
