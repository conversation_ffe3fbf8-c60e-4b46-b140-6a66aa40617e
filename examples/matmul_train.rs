use candle_core::{D<PERSON>ype, Device, Tensor};
use candle_nn::{Conv1dConfig, Optimizer, VarBuilder, VarMap, loss};
use candle_tests::mnist_matmul_optimum::MatmulLayer;
use image::{<PERSON><PERSON><PERSON><PERSON>, Lu<PERSON>};

fn main() -> anyhow::Result<()> {
    println!("loading MNIST dataset...");
    let mnist = candle_datasets::vision::mnist::load()?;
    let train_images = mnist.train_images;

    let grid_sq_base = 3;
    let mut varmap = VarMap::new();
    let vb = VarBuilder::from_varmap(&varmap, DType::F32, &Device::Cpu);
    let random_t = Tensor::rand(
        0.0f64,
        1.0f64,
        &[grid_sq_base * grid_sq_base, 784],
        &Device::Cpu,
    )?;
    let matmul_layer0 = MatmulLayer::load(&vb, random_t.clone(), "l0")?;
    let matmul_layer1 = MatmulLayer::load(&vb, random_t.clone(), "l1")?;
    let matmul_layer2 = MatmulLayer::load(&vb, random_t.clone(), "l2")?;
    //varmap = matmul_layer.init(train_images.clone(), varmap)?;

    let adamw_params = candle_nn::ParamsAdamW {
        lr: 8e-3,
        weight_decay: 0.02,
        ..Default::default()
    };
    let mut optimizer = candle_nn::AdamW::new(varmap.all_vars(), adamw_params)?;

    let train_images_dims = train_images.clone().dims().to_vec();
    let mut avg_loss = 0.0;
    for epoch in 0..10000000 {
        let index = rand::random::<u32>() % train_images_dims[1] as u32;
        let image = train_images.clone().get_on_dim(0, index as usize)?;
        let image_t = image.unsqueeze(0)?;
        let (pred_t, probs, pred_probs) = matmul_layer0.forward(&image)?;
        let (pred_t1, probs_1, pred_probs_1) = matmul_layer1.forward(&pred_t)?;
        let (pred_t2, probs_2, pred_probs_2) = matmul_layer2.forward(&pred_t1)?;

        
        if epoch == 80_000 {
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 2e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 1_000_000 {
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 1e-4,
                weight_decay: 0.02,
                ..Default::default()
            });
        }

        let l0_loss = loss::mse(&probs,&pred_probs)?;
        let l1_loss = loss::mse(&probs_1,&pred_probs_1)?;
        let l2_loss = loss::mse(&probs_2,&pred_probs_2)?;
        let loss = (l2_loss + l1_loss + l0_loss + loss::mse(&pred_t2, &image_t)?)?; //probs.sub(&label_t)?.powf(2.0)?.mean_all()?;
        
        avg_loss += loss.to_scalar::<f32>()?;
        optimizer.backward_step(&loss)?;

        if epoch % 100 == 0 {
            println!("Epoch {}: Loss = {}", epoch, avg_loss / 100.0);
            avg_loss = 0.0;
            tensor_to_image(&pred_t.reshape(&[28, 28])?, "pred_t")?;
            tensor_to_image(&pred_t1.reshape(&[28, 28])?, "pred_t1")?;
            tensor_to_image(&pred_t2.reshape(&[28, 28])?, "pred_t2")?;
            tensor_to_image(&image.reshape(&[28, 28])?, "image_t")?;

            // Create all 100 weight images as a grid of 10x10
            let weight = matmul_layer0.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights")?;

            
            let weight = matmul_layer1.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights_1")?;

            let weight = matmul_layer2.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights_2")?;

        }
    }

    Ok(())
}

fn tensor_to_image(image_tensor: &Tensor, name: &str) -> anyhow::Result<()> {
    let max_val = image_tensor.max_all()?;
    let image_tensor = image_tensor.broadcast_div(&max_val)?;

    // Convert to Vec and normalize to [0, 255]
    let data: Vec<Vec<f32>> = image_tensor.to_vec2()?;
    // Create image buffer
    let mut img_buffer =
        ImageBuffer::new(image_tensor.dims()[1] as u32, image_tensor.dims()[0] as u32);

    for (y, row) in data.iter().enumerate() {
        for (x, &pixel) in row.iter().enumerate() {
            // Assuming pixel values are in [0, 1] range
            let pixel_u8 = (pixel.clamp(0.0, 1.0) * 255.0) as u8;
            img_buffer.put_pixel(x as u32, y as u32, Luma([pixel_u8]));
        }
    }

    // Save image
    img_buffer.save(format!("{name}.png"))?;

    Ok(())
}
